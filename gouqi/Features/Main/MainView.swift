import SwiftUI
import ComposableArchitecture
import LogCore

// Tab 枚举，统一管理 tab 信息和索引
private enum MainTab: Int, CaseIterable, Hashable {
    case recommend, featured, addRole, favorite, chat

    var imageName: String {
        switch self {
        case .recommend: return "tab_recommend"
        case .featured: return "tab_featured"
        case .addRole: return "tab_add"
        case .favorite: return "tab_favorite"
        case .chat: return "tab_chat"
        }
    }
    var selectedImageName: String {
        switch self {
        case .recommend: return "tab_recommend_selected"
        case .featured: return "tab_featured_selected"
        case .addRole: return "tab_add"
        case .favorite: return "tab_favorite_selected" // 没有选中态图片则用普通
        case .chat: return "tab_chat_selected" // 没有选中态图片则用普通
        }
    }
}

// MainView 主页主界面
//
// 该界面分为两大区域：
// 1. 内容区域：占据除底部导航栏外的所有空间，实际内容由 Pager 加载，支持卡片式分页滑动。
//    顶部区域（如搜索框、头像按钮等）属于各自 Tab 的内容，不在 MainView 直接管理。
// 2. 底部区域：主导航栏，高度固定为 80，标签为 推荐：精选，新增角色，收藏，聊天。
//
// 主页本身不直接包含顶部搜索栏等 UI，仅负责内容区和底部导航栏的整体布局。
struct MainView: View {
    @Bindable var store: StoreOf<MainFeature>

    // MARK: - 缓存的scoped stores
    @State private var recommendStore: StoreOf<RecommendFeature>?
    @State private var featuredStore: StoreOf<FeaturedFeature>?
    @State private var addRoleStore: StoreOf<AddRoleFeature>?
    @State private var favoriteStore: StoreOf<FavoriteFeature>?
    @State private var chatStore: StoreOf<ChatFeature>?
    @State private var storesInitialized = false

    // MARK: - 缓存的tab视图
    @State private var cachedTabViews: [MainTab: AnyView] = [:]

    // MARK: - 计算属性

    /// 提取状态变量，避免在body中重复计算
    private var selectedTab: Int { store.navigation.selectedTab }
    private var isTransitioning: Bool { store.navigation.isTransitioning }
    private var hasErrorToShow: Bool { store.hasErrorToShow }
    private var errorMessage: String? { store.error.toastMessage }

    var body: some View {
        ZStack {
            // 内容区（不下沉，直接铺满）
            contentArea

            // 底部导航栏（浮在内容之上）
            bottomNavigationBar

            // 错误提示Toast
            if hasErrorToShow {
                errorToastView
            }
        }
        .background(Color("color-background").ignoresSafeArea())
        .ignoresSafeArea(.keyboard)
        .ignoresSafeArea(edges: .bottom)
        .onAppear {
            initializeStores() // 在onAppear中初始化stores
            // 使用DispatchQueue.main.async避免在视图更新期间修改状态
            DispatchQueue.main.async {
                store.send(.onAppear)
            }
        }
        .onDisappear {
            // 使用DispatchQueue.main.async避免在视图更新期间修改状态
            DispatchQueue.main.async {
                store.send(.onDisappear)
            }
        }
    }

    // MARK: - 子视图组件

    /// 内容区域
    private var contentArea: some View {
        TabView(selection: .constant(selectedTab)) {
            ForEach(MainTab.allCases.indices, id: \.self) { index in
                let tab = MainTab.allCases[index]
                tabView(for: tab, store: store)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    // .opacity(isTransitioning ? 0.8 : 1.0) // 切换时的视觉反馈
                    .id("tab_\(tab.rawValue)") // 为每个tab添加稳定的ID，确保视图缓存
                    .allowsHitTesting(!isTransitioning) // 切换时禁用交互，避免状态冲突
                    .tag(index)
            }
        }
        .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never)) // 隐藏页面指示器
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .animation(.easeInOut(duration: 0.2), value: isTransitioning)
        .gesture(
            DragGesture()
                .onEnded { value in
                    // 处理滑动手势切换标签
                    let threshold: CGFloat = 50
                    if value.translation.x > threshold && selectedTab > 0 {
                        // 向右滑动，切换到上一个标签
                        DispatchQueue.main.async {
                            store.send(.tabSelected(selectedTab - 1))
                        }
                    } else if value.translation.x < -threshold && selectedTab < MainTab.allCases.count - 1 {
                        // 向左滑动，切换到下一个标签
                        DispatchQueue.main.async {
                            store.send(.tabSelected(selectedTab + 1))
                        }
                    }
                }
        )
    }

    /// 底部导航栏
    private var bottomNavigationBar: some View {
        ZStack {
            VisualEffectBlur(
                colorTint: .black.opacity(0.1),
                colorTintAlpha: 0.56,
                blurRadius: 10,
                scale: 1
            )
            .frame(height: 76)
            .ignoresSafeArea(edges: .bottom)

            HStack {
                ForEach(MainTab.allCases.indices, id: \.self) { idx in
                    let tab = MainTab.allCases[idx]
                    let isSelected = selectedTab == tab.rawValue

                    tabButton(for: tab, isSelected: isSelected)

                    if idx != MainTab.allCases.count - 1 {
                        Spacer()
                    }
                }
            }
            .padding(.horizontal, 24)
        }
        .frame(height: 76)
        .frame(maxHeight: .infinity, alignment: .bottom)
        .ignoresSafeArea(edges: .bottom)
    }

    /// 错误提示Toast
    private var errorToastView: some View {
        VStack {
            Spacer()

            if let message = errorMessage {
                HStack {
                    Text(message)
                        .foregroundColor(.white)
                        .font(.system(size: 14))

                    Spacer()

                    Button("关闭") {
                        // 使用DispatchQueue.main.async避免在视图更新期间修改状态
                        DispatchQueue.main.async {
                            store.send(.dismissToast)
                        }
                    }
                    .foregroundColor(.white)
                    .font(.system(size: 14, weight: .medium))
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.red.opacity(0.9))
                .cornerRadius(8)
                .padding(.horizontal, 16)
                .padding(.bottom, 100) // 避免被底部导航栏遮挡
                .transition(.move(edge: .bottom).combined(with: .opacity))
            }
        }
        .animation(.easeInOut(duration: 0.3), value: hasErrorToShow)
    }

    /// Tab按钮组件
    @ViewBuilder
    private func tabButton(for tab: MainTab, isSelected: Bool) -> some View {
        Button {
            // 使用DispatchQueue.main.async避免在视图更新期间修改状态
            DispatchQueue.main.async {
                store.send(.tabSelected(tab.rawValue))
            }
        } label: {
            ZStack {
                let imageName = isSelected ? tab.selectedImageName : tab.imageName
                let size: CGFloat = tab == .addRole ? 55 : 48

                Image(imageName)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: size, height: size)
                    .scaleEffect(isSelected ? 1.0 : 0.9)
                    .animation(.easeInOut(duration: 0.2), value: isSelected)
            }
        }
        .disabled(isTransitioning) // 切换过程中禁用按钮
    }

    /// 初始化所有scoped stores
    private func initializeStores() {
        guard !storesInitialized else {
            return
        }

        recommendStore = store.scope(state: \.recommend, action: \.recommend)
        featuredStore = store.scope(state: \.featured, action: \.featured)
        addRoleStore = store.scope(state: \.addRole, action: \.addRole)
        favoriteStore = store.scope(state: \.favorite, action: \.favorite)
        chatStore = store.scope(state: \.chat, action: \.chat)

        storesInitialized = true

        // 清除之前的视图缓存，确保使用新的stores
        cachedTabViews.removeAll()
    }

    /// Tab视图内容
    /// 获取或创建缓存的tab视图
    private func getCachedTabView(for tab: MainTab) -> AnyView {
        // 检查缓存
        if let cachedView = cachedTabViews[tab] {
            return cachedView
        }

        // 创建新视图并缓存
        let newView = createTabView(for: tab)
        cachedTabViews[tab] = newView
        return newView
    }

    /// 创建具体的tab视图
    @ViewBuilder
    private func createTabView(for tab: MainTab) -> AnyView {
        guard storesInitialized else {
            return AnyView(
                ProgressView()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)

            )
        }

        switch tab {
        case .recommend:
            return AnyView(
                VStack {
                    Spacer()
                    Text("推荐页面")
                        .font(.title)
                        .foregroundColor(.primary)
                    Text("简单测试界面")
                        .font(.body)
                        .foregroundColor(.secondary)
                    Spacer()
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color("color-background"))
                .id("recommend_view") // 稳定的视图标识符
            )
        case .featured:
            return AnyView(
                VStack {
                    Spacer()
                    Text("精选页面")
                        .font(.title)
                        .foregroundColor(.primary)
                    Text("简单测试界面")
                        .font(.body)
                        .foregroundColor(.secondary)
                    Spacer()
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color("color-background"))
                .id("featured_view") // 稳定的视图标识符
            )
        case .addRole:
            return AnyView(
                VStack {
                    Spacer()
                    Text("新增角色页面")
                        .font(.title)
                        .foregroundColor(.primary)
                    Text("简单测试界面")
                        .font(.body)
                        .foregroundColor(.secondary)
                    Spacer()
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color("color-background"))
                .id("addRole_view") // 稳定的视图标识符
            )
        case .favorite:
            return AnyView(
                VStack {
                    Spacer()
                    Text("收藏页面")
                        .font(.title)
                        .foregroundColor(.primary)
                    Text("简单测试界面")
                        .font(.body)
                        .foregroundColor(.secondary)
                    Spacer()
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color("color-background"))
                .id("favorite_view") // 稳定的视图标识符
            )
        case .chat:
            return AnyView(
                VStack {
                    Spacer()
                    Text("聊天页面")
                        .font(.title)
                        .foregroundColor(.primary)
                    Text("简单测试界面")
                        .font(.body)
                        .foregroundColor(.secondary)
                    Spacer()
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color("color-background"))
                .id("chat_view") // 稳定的视图标识符
            )
        }
    }

    /// Tab视图内容 - 使用缓存机制
    @ViewBuilder
    private func tabView(for tab: MainTab, store: StoreOf<MainFeature>) -> some View {
        getCachedTabView(for: tab)
    }
}

#if DEBUG
struct MainView_Previews_: PreviewProvider {
    static var previews: some View {
        Group {
            // 推荐
            MainView(
                store: Store(
                    initialState: {
                        var state = MainFeature.State()
                        state.selectedTab = MainTab.recommend.rawValue
                        return state
                    }(),
                    reducer: { MainFeature() }
                )
            )
            .previewDisplayName("推荐")

            // 精选
            MainView(
                store: Store(
                    initialState: {
                        var state = MainFeature.State()
                        state.selectedTab = MainTab.featured.rawValue
                        return state
                    }(),
                    reducer: { MainFeature() }
                )
            )
            .previewDisplayName("精选")

            // 新增角色
            MainView(
                store: Store(
                    initialState: {
                        var state = MainFeature.State()
                        state.selectedTab = MainTab.addRole.rawValue
                        return state
                    }(),
                    reducer: { MainFeature() }
                )
            )
            .previewDisplayName("新增角色")

            // 收藏
            MainView(
                store: Store(
                    initialState: {
                        var state = MainFeature.State()
                        state.selectedTab = MainTab.favorite.rawValue
                        return state
                    }(),
                    reducer: { MainFeature() }
                )
            )
            .previewDisplayName("收藏")

            // 聊天
            MainView(
                store: Store(
                    initialState: {
                        var state = MainFeature.State()
                        state.selectedTab = MainTab.chat.rawValue
                        return state
                    }(),
                    reducer: { MainFeature() }
                )
            )
            .previewDisplayName("聊天")
        }
    }
}
#endif
  
  

