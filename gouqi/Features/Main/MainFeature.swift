import ComposableArchitecture
import LogCore
import Foundation

// 导入导航上下文（从 AppFeature 中）
// 注意：在实际项目中，建议将 NavigationContext 移到共享模块中

@Reducer
struct MainFeature {
    // MARK: - 子State定义

    /// 导航相关状态
    @ObservableState
    struct NavigationState: Equatable {
        var selectedTab = 0
        var previousTab = 0 // 记录上一个选中的tab，用于优化
        var isTransitioning = false // 是否正在切换tab

        static func == (lhs: NavigationState, rhs: NavigationState) -> Bool {
            lhs.selectedTab == rhs.selectedTab &&
            lhs.previousTab == rhs.previousTab &&
            lhs.isTransitioning == rhs.isTransitioning
        }
    }

    /// 应用生命周期状态
    @ObservableState
    struct LifecycleState: Equatable {
        var hasInitialized = false
        var isAppActive = true
        var lastActiveTime: Date? = nil
    }

    /// 错误处理状态
    @ObservableState
    struct ErrorState: Equatable {
        var toastMessage: String? = nil
        var lastError: String? = nil
    }

    @ObservableState
    struct State: Equatable {
        static func == (lhs: MainFeature.State, rhs: MainFeature.State) -> Bool {
            lhs.navigation == rhs.navigation &&
            lhs.lifecycle == rhs.lifecycle &&
            lhs.error == rhs.error &&
            lhs.recommend == rhs.recommend &&
            lhs.featured == rhs.featured &&
            lhs.addRole == rhs.addRole &&
            lhs.favorite == rhs.favorite &&
            lhs.chat == rhs.chat
        }

        // 分组状态
        var navigation = NavigationState()
        var lifecycle = LifecycleState()
        var error = ErrorState()

        // 子Feature状态
        var recommend = RecommendFeature.State()
        var featured = FeaturedFeature.State()
        var addRole = AddRoleFeature.State()
        var favorite = FavoriteFeature.State()
        var chat = ChatFeature.State()

        // MARK: - 计算属性

        /// 当前选中的tab索引
        var selectedTab: Int {
            get { navigation.selectedTab }
            set {
                // 移除直接的状态修改，改为通过Action来处理
                // 这里只是一个计算属性，不应该直接修改状态
                // 状态修改应该通过 tabSelected action 来处理
            }
        }

        /// 是否需要初始化
        var needsInitialization: Bool {
            !lifecycle.hasInitialized
        }

        /// 是否有错误消息需要显示
        var hasErrorToShow: Bool {
            error.toastMessage != nil
        }
    }

    // MARK: - 初始化任务枚举
    enum InitializationTask {
        case checkAppState
        case initializeSubFeatures
        case restoreLastActiveTab
    }

    @CasePathable
    enum Action: BindableAction, Equatable {
        case binding(BindingAction<State>)

        // MARK: - 生命周期相关
        case onAppear
        case onDisappear
        case appDidBecomeActive
        case appDidEnterBackground

        // MARK: - 导航相关
        case tabSelected(Int)
        case setTabTransitioning(Bool)

        // MARK: - 错误处理
        case dismissToast
        case showError(String)

        // MARK: - 用户操作
        case logout

        // MARK: - 子Feature Actions
        case recommend(RecommendFeature.Action)
        case featured(FeaturedFeature.Action)
        case addRole(AddRoleFeature.Action)
        case favorite(FavoriteFeature.Action)
        case chat(ChatFeature.Action)

        // MARK: - 委托Actions
        case delegate(Delegate)

        // MARK: - 私有Actions（仅在Reducer内部使用）
        case _handleInitializationComplete
        case _handleTabSwitchComplete(Int)
        case _handleError(String)

        @CasePathable
        enum Delegate: Equatable {
            case logoutRequested(context: NavigationContext)
            case loginRequested(context: NavigationContext)
            case navigateToProfile
            case requireLogin
        }

        // MARK: - Equatable 实现
        static func == (lhs: Action, rhs: Action) -> Bool {
            switch (lhs, rhs) {
            case (.binding(let lhsAction), .binding(let rhsAction)):
                return lhsAction == rhsAction
            case (.onAppear, .onAppear),
                 (.onDisappear, .onDisappear),
                 (.appDidBecomeActive, .appDidBecomeActive),
                 (.appDidEnterBackground, .appDidEnterBackground),
                 (.dismissToast, .dismissToast),
                 (.logout, .logout),
                 (._handleInitializationComplete, ._handleInitializationComplete):
                return true
            case (.tabSelected(let lhsIndex), .tabSelected(let rhsIndex)):
                return lhsIndex == rhsIndex
            case (.setTabTransitioning(let lhsBool), .setTabTransitioning(let rhsBool)):
                return lhsBool == rhsBool
            case (.showError(let lhsError), .showError(let rhsError)):
                return lhsError == rhsError
            case (._handleTabSwitchComplete(let lhsIndex), ._handleTabSwitchComplete(let rhsIndex)):
                return lhsIndex == rhsIndex
            case (._handleError(let lhsError), ._handleError(let rhsError)):
                return lhsError == rhsError
            case (.recommend(let lhsAction), .recommend(let rhsAction)):
                return lhsAction == rhsAction
            case (.featured(let lhsAction), .featured(let rhsAction)):
                return lhsAction == rhsAction
            case (.addRole(let lhsAction), .addRole(let rhsAction)):
                return lhsAction == rhsAction
            case (.favorite(let lhsAction), .favorite(let rhsAction)):
                return lhsAction == rhsAction
            case (.chat(let lhsAction), .chat(let rhsAction)):
                return lhsAction == rhsAction
            case (.delegate(let lhsDelegate), .delegate(let rhsDelegate)):
                return lhsDelegate == rhsDelegate
            default:
                return false
            }
        }
    }

    @Dependency(\.userRepository) var userRepository

    // MARK: - 辅助方法

    /// 获取需要执行的初始化任务
    private func getInitializationTasks(for state: State) -> [InitializationTask] {
        var tasks: [InitializationTask] = []

        if !state.lifecycle.hasInitialized {
            tasks.append(.checkAppState)
            tasks.append(.initializeSubFeatures)

            // 如果有上次活跃的tab记录，恢复它
            if state.navigation.previousTab != state.navigation.selectedTab {
                tasks.append(.restoreLastActiveTab)
            }
        }

        return tasks
    }

    /// 执行初始化任务
    private func executeInitializationTasks(_ tasks: [InitializationTask], state: inout State) -> Effect<Action> {
        guard !tasks.isEmpty else { return .none }

//        XLog.i("MainFeature 执行初始化任务: \(tasks)")

        var effects: [Effect<Action>] = []

        for task in tasks {
            switch task {
            case .checkAppState:
                state.lifecycle.isAppActive = true
                state.lifecycle.lastActiveTime = Date()

            case .initializeSubFeatures:
                // 暂时禁用子Feature初始化，测试是否是网络请求导致的问题
                // let currentTab = state.navigation.selectedTab
                // if currentTab == 0 { // Recommend tab
                //     effects.append(.send(.recommend(.onAppear)))
                // }
                break

            case .restoreLastActiveTab:
                // 这里可以添加恢复上次活跃tab的逻辑
                break
            }
        }

        state.lifecycle.hasInitialized = true
        effects.append(.send(._handleInitializationComplete))

        return .merge(effects)
    }

    /// 处理tab切换的优化逻辑
    private func handleTabSwitch(to newTab: Int, from oldTab: Int, state: inout State) -> Effect<Action> {
        // 注意：此时selectedTab已经在tabSelected action中更新了
        // 这里不再重复更新selectedTab

        var effects: [Effect<Action>] = []

        // 使用异步方式处理切换逻辑，避免在状态更新期间立即修改其他状态
        effects.append(
            .run { send in
                // 设置切换状态
                await send(.setTabTransitioning(true))

                // 通知旧tab失活
                switch oldTab {
                case 0: await send(.recommend(.setTabActive(false)))
                case 1: await send(.featured(.setTabActive(false)))
                default: break
                }

                // 通知新tab激活
                switch newTab {
                case 0: await send(.recommend(.setTabActive(true)))
                case 1: await send(.featured(.setTabActive(true)))
                default: break
                }

                // 延迟完成切换状态
                try await Task.sleep(for: .milliseconds(100))
                await send(._handleTabSwitchComplete(newTab))
            }
        )

        return .merge(effects)
    }

    var body: some ReducerOf<Self> {
        BindingReducer()

        // 注册各子 Feature 的 Scope，保证事件链路完整
        Scope(state: \.recommend, action: \.recommend) { RecommendFeature() }
        Scope(state: \.featured, action: \.featured) { FeaturedFeature() }
        Scope(state: \.addRole, action: \.addRole) { AddRoleFeature() }
        Scope(state: \.favorite, action: \.favorite) { FavoriteFeature() }
        Scope(state: \.chat, action: \.chat) { ChatFeature() }

        Reduce<State, Action> { state, action in
            switch action {
            // MARK: - 生命周期处理
            case .onAppear:
                // 避免重复初始化
                guard state.needsInitialization else { return .none }

                // 执行初始化任务
                let tasks = getInitializationTasks(for: state)
                return executeInitializationTasks(tasks, state: &state)

            case .onDisappear:
                state.lifecycle.isAppActive = false
                return .none

            case .appDidBecomeActive:
                state.lifecycle.isAppActive = true
                state.lifecycle.lastActiveTime = Date()
                return .none

            case .appDidEnterBackground:
                state.lifecycle.isAppActive = false
                return .none

            // MARK: - 导航处理
            case let .tabSelected(index):
                let oldTab = state.navigation.selectedTab
                // 避免重复切换到同一个tab
                guard index != oldTab else { return .none }

                // 先更新previousTab，再更新selectedTab
                state.navigation.previousTab = oldTab
                state.navigation.selectedTab = index

                return handleTabSwitch(to: index, from: oldTab, state: &state)



            case let .setTabTransitioning(isTransitioning):
                state.navigation.isTransitioning = isTransitioning
                return .none

            // MARK: - 错误处理
            case let .showError(message):
                state.error.toastMessage = message
                state.error.lastError = message

                // 3秒后自动关闭toast
                return .run { send in
                    try await Task.sleep(for: .seconds(3))
                    await send(.dismissToast)
                }

            case .dismissToast:
                state.error.toastMessage = nil
                return .none

            // MARK: - 用户操作
            case .logout:
                return .run { send in
                    // 使用新的委托方法，明确指定为用户主动操作
                    await send(.delegate(.logoutRequested(context: .userAction)))
                }

            // MARK: - 私有Actions处理
            case ._handleInitializationComplete:
                return .none

            case let ._handleTabSwitchComplete(tabIndex):
                state.navigation.isTransitioning = false
                return .none

            case let ._handleError(error):
                return .send(.showError(error))

            // MARK: - 委托处理
            case .delegate:
                return .none

            // MARK: - 子Feature委托转发
            case let .recommend(.delegate(.loginRequested(context))):
                // 从推荐页面跳转登录
                return .send(.delegate(.loginRequested(context: context)))

            case .recommend(.delegate(.navigateToProfile)):
                return .send(.delegate(.navigateToProfile))

            case let .featured(.delegate(.loginRequested(context))):
                // 从精选页面跳转登录
                return .send(.delegate(.loginRequested(context: context)))

            case .featured(.delegate(.navigateToProfile)):
                return .send(.delegate(.navigateToProfile))

            // MARK: - 其他子Feature Actions
            case .recommend, .featured, .addRole, .favorite, .chat:
                return .none

            case .binding:
                // 暂时禁用 binding 处理，测试是否是绑定导致的问题
                print("⚠️ MainFeature.binding action 被调用")
                return .none
            }
        }
    }
}

